{% extends 'base.html' %}

{% block title %}Create Template - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create SMS Template</h1>
            <p class="mt-2 text-gray-600">Create a reusable SMS template with variables</p>
        </div>

        <!-- Form -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Template Details</h2>
            </div>
            <form method="post" class="p-6">
                {% csrf_token %}
                <div class="space-y-6">
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tag mr-2"></i>Template Name
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-file-alt mr-2"></i>Template Content
                        </label>
                        {{ form.content }}
                        <p class="mt-2 text-xs text-gray-500">Use double curly braces for variables, e.g., {{name}}, {{amount}}, etc.</p>
                        {% if form.content.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.content.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.variables.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-code mr-2"></i>Variables
                        </label>
                        {{ form.variables }}
                        <p class="mt-2 text-xs text-gray-500">Enter variable names separated by commas (e.g., name, amount, date)</p>
                        {% if form.variables.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.variables.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Preview -->
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-eye mr-2"></i>Preview
                        </h3>
                        <div id="preview" class="text-sm text-gray-600 bg-white p-3 rounded border min-h-16">
                            Enter content to see preview...
                        </div>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{% url 'template_list' %}" class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-purple-600 text-white rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>Create Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Live preview
    const contentInput = document.getElementById('{{ form.content.id_for_label }}');
    const preview = document.getElementById('preview');
    
    contentInput.addEventListener('input', function() {
        const content = this.value.trim();
        if (content) {
            preview.textContent = content;
        } else {
            preview.textContent = 'Enter content to see preview...';
        }
    });
    
    // Initial preview
    if (contentInput.value.trim()) {
        preview.textContent = contentInput.value;
    }
</script>
{% endblock %}