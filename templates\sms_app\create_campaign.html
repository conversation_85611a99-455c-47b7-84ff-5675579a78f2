{% extends 'base.html' %}

{% block title %}Create Campaign - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create SMS Campaign</h1>
            <p class="mt-2 text-gray-600">Create a new SMS marketing campaign</p>
        </div>

        <!-- Form -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Campaign Details</h2>
            </div>
            <form method="post" class="p-6">
                {% csrf_token %}
                <div class="space-y-6">
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-bullhorn mr-2"></i>Campaign Name
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.template.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-file-alt mr-2"></i>Template
                        </label>
                        {{ form.template }}
                        {% if form.template.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.template.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.recipients.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-users mr-2"></i>Recipients
                        </label>
                        {{ form.recipients }}
                        <div class="mt-2">
                            <p class="text-xs text-gray-500">Enter phone numbers separated by commas or new lines</p>
                            <p class="text-xs text-gray-500 mt-1">
                                Recipients count: <span id="recipient-count" class="font-medium">0</span>
                            </p>
                        </div>
                        {% if form.recipients.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.recipients.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.scheduled_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-clock mr-2"></i>Scheduled Time (Optional)
                        </label>
                        {{ form.scheduled_time }}
                        <p class="mt-2 text-xs text-gray-500">Leave empty to send immediately</p>
                        {% if form.scheduled_time.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.scheduled_time.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Cost Estimate -->
                    <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                        <div class="flex items-center">
                            <i class="fas fa-calculator text-orange-600 mr-2"></i>
                            <h3 class="text-sm font-medium text-orange-900">Campaign Summary</h3>
                        </div>
                        <div class="mt-2 text-sm text-orange-700">
                            <p>Recipients: <span id="recipient-count-display">0</span></p>
                            <p>Estimated cost: $<span id="cost-estimate">0.00</span></p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{% url 'campaign_list' %}" class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-orange-600 text-white rounded-lg text-sm font-medium hover:bg-orange-700 transition-colors duration-200">
                        <i class="fas fa-rocket mr-2"></i>Create Campaign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Recipient counter and cost calculator
    const recipientsInput = document.getElementById('{{ form.recipients.id_for_label }}');
    const recipientCount = document.getElementById('recipient-count');
    const recipientCountDisplay = document.getElementById('recipient-count-display');
    const costEstimate = document.getElementById('cost-estimate');
    
    function updateRecipientCount() {
        const recipients = recipientsInput.value.trim();
        if (!recipients) {
            recipientCount.textContent = '0';
            recipientCountDisplay.textContent = '0';
            costEstimate.textContent = '0.00';
            return;
        }
        
        const recipientList = recipients.split(/[,\n]/).filter(r => r.trim());
        const count = recipientList.length;
        
        recipientCount.textContent = count;
        recipientCountDisplay.textContent = count;
        costEstimate.textContent = (count * 0.05).toFixed(2);
    }
    
    recipientsInput.addEventListener('input', updateRecipientCount);
    
    // Initial count
    updateRecipientCount();
</script>
{% endblock %}