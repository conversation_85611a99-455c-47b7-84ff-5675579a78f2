from rest_framework import serializers
from .models import SMSMessage, SMSTemplate, SMSCampaign


class SMSMessageSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = SMSMessage
        fields = [
            'id', 'from_address', 'to_address', 'message', 'status', 
            'message_id', 'error_message', 'created_at', 'sent_at', 
            'delivered_at', 'created_by', 'cost', 'character_count'
        ]
        read_only_fields = [
            'id', 'status', 'message_id', 'error_message', 'created_at', 
            'sent_at', 'delivered_at', 'created_by', 'cost', 'character_count'
        ]


class SMSTemplateSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = SMSTemplate
        fields = [
            'id', 'name', 'content', 'variables', 'created_by', 
            'created_at', 'is_active'
        ]
        read_only_fields = ['id', 'created_by', 'created_at']


class SMSCampaignSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(read_only=True)
    template = SMSTemplateSerializer(read_only=True)
    template_id = serializers.IntegerField(write_only=True)
    recipient_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SMSCampaign
        fields = [
            'id', 'name', 'template', 'template_id', 'recipients', 
            'scheduled_time', 'created_by', 'created_at', 'is_sent',
            'recipient_count'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'is_sent']
    
    def get_recipient_count(self, obj):
        return obj.get_recipient_count()