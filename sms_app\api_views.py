from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SMSMessage, SMSTemplate, SMSCampaign
from .serializers import SMSMessageSerializer, SMSTemplateSerializer, SMSCampaignSerializer
from .tasks import send_sms, send_bulk_sms


class SMSMessageViewSet(viewsets.ModelViewSet):
    queryset = SMSMessage.objects.all()
    serializer_class = SMSMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = SMSMessage.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        return queryset.order_by('-created_at')


class SMSTemplateViewSet(viewsets.ModelViewSet):
    queryset = SMSTemplate.objects.filter(is_active=True)
    serializer_class = SMSTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class SMSCampaignViewSet(viewsets.ModelViewSet):
    queryset = SMSCampaign.objects.all()
    serializer_class = SMSCampaignSerializer
    permission_classes = [IsAuthenticated]
    
    def perform_create(self, serializer):
        campaign = serializer.save(created_by=self.request.user)
        if not campaign.scheduled_time:
            send_bulk_sms.delay(campaign.id)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_sms_api(request):
    """API endpoint to send a single SMS"""
    serializer = SMSMessageSerializer(data=request.data)
    if serializer.is_valid():
        sms_message = serializer.save(created_by=request.user)
        send_sms.delay(sms_message.id)
        return Response({
            'message': 'SMS queued for sending',
            'sms_id': sms_message.id
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_bulk_sms_api(request):
    """API endpoint to send bulk SMS"""
    recipients = request.data.get('recipients', [])
    message = request.data.get('message', '')
    from_address = request.data.get('from_address', '908')
    
    if not recipients or not message:
        return Response({'error': 'Recipients and message are required'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    sms_messages = []
    for recipient in recipients:
        sms_message = SMSMessage.objects.create(
            from_address=from_address,
            to_address=recipient,
            message=message,
            created_by=request.user,
            status='pending'
        )
        sms_messages.append(sms_message)
        send_sms.delay(sms_message.id)
    
    return Response({
        'message': f'Queued {len(sms_messages)} SMS messages',
        'sms_ids': [sms.id for sms in sms_messages]
    }, status=status.HTTP_201_CREATED)