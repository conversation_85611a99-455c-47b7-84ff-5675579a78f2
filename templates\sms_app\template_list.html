{% extends 'base.html' %}

{% block title %}SMS Templates - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">SMS Templates</h1>
                <p class="mt-2 text-gray-600">Create and manage reusable SMS templates</p>
            </div>
            <a href="{% url 'create_template' %}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>Create Template
            </a>
        </div>
    </div>

    <!-- Templates Grid -->
    {% if templates %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for template in templates %}
                <div class="bg-white shadow-lg rounded-lg border border-gray-200 hover:shadow-xl transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">{{ template.name }}</h3>
                            <span class="text-xs text-gray-500">{{ template.created_at|date:"M d, Y" }}</span>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">{{ template.content|slice:":100" }}{% if template.content|length > 100 %}...{% endif %}</p>
                        </div>
                        
                        {% if template.variables %}
                            <div class="mb-4">
                                <p class="text-xs font-medium text-gray-700 mb-2">Variables:</p>
                                <div class="flex flex-wrap gap-1">
                                    {% for variable in template.variables %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ variable }}
                                        </span>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>By {{ template.created_by.username }}</span>
                            <div class="flex space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800 transition-colors duration-200">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <i class="fas fa-file-alt text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No templates yet</h3>
            <p class="text-gray-500 mb-6">Create your first SMS template to get started</p>
            <a href="{% url 'create_template' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                <i class="fas fa-plus mr-2"></i>Create Template
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}