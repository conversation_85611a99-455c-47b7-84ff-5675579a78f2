{% extends 'base.html' %}

{% block title %}Send SMS - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Send SMS</h1>
            <p class="mt-2 text-gray-600">Send a single SMS message to a recipient</p>
        </div>

        <!-- Form -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Message Details</h2>
            </div>
            <form method="post" class="p-6">
                {% csrf_token %}
                <div class="space-y-6">
                    <div>
                        <label for="{{ form.from_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>From Address
                        </label>
                        {{ form.from_address }}
                        {% if form.from_address.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.from_address.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.to_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-phone mr-2"></i>To Address
                        </label>
                        {{ form.to_address }}
                        {% if form.to_address.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.to_address.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.message.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2"></i>Message
                        </label>
                        {{ form.message }}
                        <div class="flex justify-between mt-2">
                            <p class="text-xs text-gray-500">Maximum 160 characters</p>
                            <p class="text-xs text-gray-500">
                                <span id="char-count">0</span>/160 characters
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{% url 'dashboard' %}" class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-paper-plane mr-2"></i>Send SMS
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Character counter
    const messageInput = document.getElementById('{{ form.message.id_for_label }}');
    const charCount = document.getElementById('char-count');
    
    messageInput.addEventListener('input', function() {
        charCount.textContent = this.value.length;
        if (this.value.length > 160) {
            charCount.style.color = '#dc2626';
        } else {
            charCount.style.color = '#6b7280';
        }
    });
    
    // Initial count
    charCount.textContent = messageInput.value.length;
</script>
{% endblock %}