from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON>er
from . import api_views

router = DefaultRouter()
router.register(r'sms', api_views.SMSMessageViewSet)
router.register(r'templates', api_views.SMSTemplateViewSet)
router.register(r'campaigns', api_views.SMSCampaignViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('send-sms/', api_views.send_sms_api, name='api_send_sms'),
    path('send-bulk-sms/', api_views.send_bulk_sms_api, name='api_send_bulk_sms'),
]