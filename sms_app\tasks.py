# from celery import shared_task
# from smpplib import client, smpp
# import smpplib
# import logging
# from django.conf import settings
# from .models import SMSMessage
# from django.utils import timezone

# logger = logging.getLogger(__name__)

# @shared_task
# def send_sms(sms_id):
#     """
#     Send an SMS using SMPP protocol.
#     Args:
#         sms_id (int): ID of the SMSMessage object
#     """
#     try:
#         # Get the SMS message object
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("sms_message*****", sms_message)
        
#         # Initialize SMPP client
#         smpp_client = client.Client(
#             host=settings.SMPP_CONFIG['HOST'], 
#             port=settings.SMPP_CONFIG['PORT'], 
#             allow_unknown_opt_params=True
#         )

#         # Bind to the SMPP server as a transceiver
#         smpp_client.connect()
#         smpp_client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             system_type='',
#             interface_version=0x34,  # SMPP v3.4
#             addr_ton=1,
#             addr_npi=1,
#             address_range=''
#         )

#         # Handler for incoming PDUs (e.g., delivery receipts or inbound messages)
#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 message_content = pdu.short_message.decode('utf-8', errors='ignore')
#                 logger.info(f"Received message: {message_content}")
#                 # Send response for deliver_sm
#                 smpp_client.send_pdu(smpp.make_pdu('deliver_sm_resp', sequence=pdu.sequence))
                
#                 # Update delivery status if this is a delivery receipt
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         smpp_client.set_message_received_handler(handle_pdu)

#         # Send the SMS
#         command = smpp.make_pdu(
#             'submit_sm',
#             source_addr_ton=1,
#             source_addr_npi=1,
#             dest_addr_ton=1,
#             dest_addr_npi=1,
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             registered_delivery=1,
#             data_coding=0,  # Use 8 for Unicode if needed
#             short_message=sms_message.message.encode('utf-8')
#         )
#         smpp_client.send_pdu(command)
#         response = smpp_client.read_pdu()

#         if response.command_status == 0:
#             logger.info(f"Message sent, ID: {response.message_id}")
#             sms_message.status = 'sent'
#             sms_message.message_id = response.message_id
#             sms_message.sent_at = timezone.now()
#         else:
#             logger.error(f"Failed to send message: {response.command_status}")
#             sms_message.status = 'failed'
#             sms_message.error_message = f"SMPP Error: {response.command_status}"

#         sms_message.save()

#         # Keep the connection alive for a short period to receive DLRs
#         smpp_client.listen(timeout=5)

#         # Unbind and disconnect
#         smpp_client.unbind()
#         smpp_client.disconnect()

#         return f"SMS sent successfully with ID: {response.message_id if response.command_status == 0 else 'Failed'}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS message with ID {sms_id} not found")
#         return f"SMS message with ID {sms_id} not found"
#     except Exception as e:
#         logger.error(f"SMPP error: {str(e)}")
#         try:
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         except:
#             pass
#         raise


# @shared_task
# def send_bulk_sms(campaign_id):
#     """
#     Send bulk SMS for a campaign.
#     """
#     from .models import SMSCampaign
    
#     try:
#         campaign = SMSCampaign.objects.get(id=campaign_id)
#         recipients = [r.strip() for r in campaign.recipients.replace('\n', ',').split(',') if r.strip()]
        
#         sent_count = 0
#         for recipient in recipients:
#             # Create SMS message
#             sms_message = SMSMessage.objects.create(
#                 to_address=recipient,
#                 message=campaign.template.content,
#                 created_by=campaign.created_by,
#                 status='pending'
#             )
            
#             # Queue individual SMS
#             send_sms.delay(sms_message.id)
#             sent_count += 1
        
#         campaign.is_sent = True
#         campaign.save()
        
#         return f"Queued {sent_count} SMS messages for campaign: {campaign.name}"
        
#     except Exception as e:
#         logger.error(f"Bulk SMS error: {str(e)}")
#         raise




# tasks.py
from celery import shared_task
from smpplib.client import Client
from smpplib import consts
from django.conf import settings
from .models import SMSMessage, SMSCampaign
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

# @shared_task
# def send_sms(sms_id):
#     try:
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("SMS_Message************************************************************************************************************", sms_message)

#         client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,
#             addr_ton=consts.SMPP_TON_INTL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         ) 

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
#                 client.send_pdu(pdu.require_ack())
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         client.set_message_received_handler(handle_pdu)

#         encoded_msg = sms_message.message.encode('utf-16be')

#         pdu = client.send_message(
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             short_message=encoded_msg,
#             data_coding=0x08,
#             registered_delivery=True,
#             source_addr_ton=consts.SMPP_TON_INTL,
#             source_addr_npi=consts.SMPP_NPI_ISDN,
#             dest_addr_ton=consts.SMPP_TON_INTL,
#             dest_addr_npi=consts.SMPP_NPI_ISDN,
#         )

#         sms_message.status = 'sent'
#         sms_message.message_id = pdu.message_id
#         sms_message.sent_at = timezone.now()
#         sms_message.save()

#         client.listen(timeout=5)
#         client.unbind()
#         client.disconnect()
#         return f"SMS sent with ID {pdu.message_id}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS with ID {sms_id} does not exist")
#         return "SMS not found"
#     except Exception as e:
#         logger.exception("Failed to send SMS")
#         if 'sms_message' in locals():
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         raise


# @shared_task
# def send_sms(sms_id):
#     try:
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("SMS_Message************************************************************************************************************", sms_message)

#         client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,
#             addr_ton=consts.SMPP_TON_INTL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         )

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
#                 client.send_pdu(pdu.require_ack())
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         client.set_message_received_handler(handle_pdu)

#         encoded_msg = sms_message.message.encode('utf-16be')

#         # Send the message and capture the response
#         response = client.send_message(
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             short_message=encoded_msg,
#             data_coding=0x08,
#             registered_delivery=True,
#             source_addr_ton=consts.SMPP_TON_INTL,
#             source_addr_npi=consts.SMPP_NPI_ISDN,
#             dest_addr_ton=consts.SMPP_TON_INTL,
#             dest_addr_npi=consts.SMPP_NPI_ISDN,
#         )

#         # Check if the response is a submit_sm_resp PDU and extract message_id
#         if response.command == 'submit_sm_resp' and hasattr(response, 'message_id'):
#             sms_message.message_id = response.message_id
#         else:
#             raise Exception("Failed to retrieve message_id from submit_sm_resp")

#         sms_message.status = 'sent'
#         sms_message.sent_at = timezone.now()
#         sms_message.save()

#         client.listen(timeout=5)
#         client.unbind()
#         client.disconnect()
#         return f"SMS sent with ID {sms_message.message_id}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS with ID {sms_id} does not exist")
#         return "SMS not found"
#     except Exception as e:
#         logger.exception("Failed to send SMS")
#         if 'sms_message' in locals():
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         raise


@shared_task
def send_sms(sms_id):
    try:
        sms_message = SMSMessage.objects.get(id=sms_id)

        print("SMS_Message************************************************************************************************************", sms_message)

        client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
        client.connect()
        client.bind_transceiver(
            system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
            password=settings.SMPP_CONFIG['PASSWORD'],
            interface_version=0x34,
            # addr_ton=consts.SMPP_TON_INTL,
            addr_ton=consts.SMPP_TON_NATNL,
            addr_npi=consts.SMPP_NPI_ISDN,
            address_range=''
        )

        def handle_pdu(pdu):
            if pdu.command == 'deliver_sm':
                try:
                    message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
                except Exception:
                    message = str(pdu.short_message)
                logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
                client.send_pdu(pdu.require_ack())
                if hasattr(pdu, 'receipted_message_id'):
                    try:
                        msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
                        msg.status = 'delivered'
                        msg.delivered_at = timezone.now()
                        msg.save()
                    except SMSMessage.DoesNotExist:
                        pass

        client.set_message_received_handler(handle_pdu)

        encoded_msg = sms_message.message.encode('utf-16be')

        # Send the message and capture the response
        response = client.send_message(
            source_addr=sms_message.from_address,
            destination_addr=sms_message.to_address,
            short_message=encoded_msg,
            data_coding=0x08,
            registered_delivery=True,
            # source_addr_ton=consts.SMPP_TON_INTL,
            source_addr_npi=consts.SMPP_NPI_ISDN,
            # dest_addr_ton=consts.SMPP_TON_INTL,
            dest_addr_npi=consts.SMPP_NPI_ISDN,

            source_addr_ton=consts.SMPP_TON_NATNL,
            dest_addr_ton=consts.SMPP_TON_NATNL,
        )

        # Debug the response
        logger.info(f"Response command: {getattr(response, 'command', 'No command attribute')}")
        logger.info(f"Response attributes: {dir(response)}")
        logger.info(f"Response content: {response}")

        # Check if the response is a submit_sm_resp PDU and extract message_id
        if getattr(response, 'command', None) == 'submit_sm_resp' and hasattr(response, 'message_id'):
            sms_message.message_id = response.message_id
            logger.info(f"Message ID: {sms_message.message_id}")
        else:
            logger.error(f"Failed to retrieve message_id. Command: {getattr(response, 'command', 'Unknown')}, Has message_id: {hasattr(response, 'message_id')}")
            raise Exception("Failed to retrieve message_id from submit_sm_resp")

        sms_message.status = 'sent'
        sms_message.sent_at = timezone.now()
        sms_message.save()

        client.listen(timeout=5)
        client.unbind()
        client.disconnect()
        return f"SMS sent with ID {sms_message.message_id}"

    except SMSMessage.DoesNotExist:
        logger.error(f"SMS with ID {sms_id} does not exist")
        return "SMS not found"
    except Exception as e:
        logger.exception("Failed to send SMS")
        if 'sms_message' in locals():
            sms_message.status = 'failed'
            sms_message.error_message = str(e)
            sms_message.save()
        raise

@shared_task
def send_bulk_sms(campaign_id):
    try:
        campaign = SMSCampaign.objects.get(id=campaign_id)
        recipients = [r.strip() for r in campaign.recipients.replace('\n', ',').split(',') if r.strip()]

        count = 0
        for recipient in recipients:
            sms_message = SMSMessage.objects.create(
                to_address=recipient,
                message=campaign.template.content,
                created_by=campaign.created_by,
                status='pending'
            )
            send_sms.delay(sms_message.id)
            count += 1

        campaign.is_sent = True
        campaign.save()

        return f"Queued {count} messages for campaign {campaign.name}"

    except Exception as e:
        logger.exception("Failed to send bulk SMS")
        raise
