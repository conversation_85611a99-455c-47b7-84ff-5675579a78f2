from django.contrib import admin
from .models import SMSMessage, SMSTemplate, SMSCampaign


@admin.register(SMSMessage)
class SMSMessageAdmin(admin.ModelAdmin):
    list_display = ['to_address', 'from_address', 'status', 'created_at', 'sent_at']
    list_filter = ['status', 'created_at', 'sent_at']
    search_fields = ['to_address', 'from_address', 'message', 'message_id']
    readonly_fields = ['created_at', 'sent_at', 'delivered_at', 'character_count']
    
    fieldsets = (
        ('Message Details', {
            'fields': ('from_address', 'to_address', 'message', 'character_count')
        }),
        ('Status', {
            'fields': ('status', 'message_id', 'error_message')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'sent_at', 'delivered_at')
        }),
        ('Metadata', {
            'fields': ('created_by', 'cost')
        }),
    )


@admin.register(SMSTemplate) 
class SMSTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_by', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'content']


@admin.register(SMSCampaign)
class SMSCampaignAdmin(admin.ModelAdmin):
    list_display = ['name', 'template', 'created_by', 'scheduled_time', 'is_sent']
    list_filter = ['is_sent', 'created_at', 'scheduled_time']
    search_fields = ['name']