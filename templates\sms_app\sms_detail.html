{% extends 'base.html' %}

{% block title %}SMS Detail - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-3xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">SMS Details</h1>
                    <p class="mt-2 text-gray-600">Message ID: {{ sms.id }}</p>
                </div>
                <a href="{% url 'sms_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to List
                </a>
            </div>
        </div>

        <!-- SMS Details Card -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Message Information</h2>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {% if sms.status == 'sent' %}bg-green-100 text-green-800
                        {% elif sms.status == 'delivered' %}bg-blue-100 text-blue-800
                        {% elif sms.status == 'failed' %}bg-red-100 text-red-800
                        {% elif sms.status == 'pending' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        <i class="fas fa-circle mr-2 text-xs"></i>{{ sms.get_status_display }}
                    </span>
                </div>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Info -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">From Address</label>
                            <div class="flex items-center">
                                <i class="fas fa-user text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.from_address }}</span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">To Address</label>
                            <div class="flex items-center">
                                <i class="fas fa-phone text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.to_address }}</span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Message ID</label>
                            <div class="flex items-center">
                                <i class="fas fa-fingerprint text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.message_id|default:"Not assigned yet" }}</span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Character Count</label>
                            <div class="flex items-center">
                                <i class="fas fa-text-width text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.character_count }} characters</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Timestamps -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created At</label>
                            <div class="flex items-center">
                                <i class="fas fa-calendar-plus text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.created_at|date:"F d, Y H:i:s" }}</span>
                            </div>
                        </div>
                        
                        {% if sms.sent_at %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sent At</label>
                            <div class="flex items-center">
                                <i class="fas fa-paper-plane text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.sent_at|date:"F d, Y H:i:s" }}</span>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if sms.delivered_at %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Delivered At</label>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.delivered_at|date:"F d, Y H:i:s" }}</span>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if sms.created_by %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                            <div class="flex items-center">
                                <i class="fas fa-user-circle text-gray-400 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ sms.created_by.username }}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Message Content -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Message Content</label>
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ sms.message }}</p>
                    </div>
                </div>
                
                <!-- Error Message -->
                {% if sms.error_message %}
                <div class="mt-6">
                    <label class="block text-sm font-medium text-red-700 mb-2">Error Message</label>
                    <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                        <p class="text-sm text-red-900">{{ sms.error_message }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Actions -->
        <div class="mt-6 flex justify-center space-x-4">
            {% if sms.status == 'failed' %}
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                    <i class="fas fa-redo mr-2"></i>Retry Send
                </button>
            {% endif %}
            <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                <i class="fas fa-print mr-2"></i>Print
            </button>
        </div>
    </div>
</div>
{% endblock %}