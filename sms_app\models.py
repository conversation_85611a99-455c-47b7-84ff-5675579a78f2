from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class SMSMessage(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('rejected', 'Rejected'),
    ]

    # Message details
    from_address = models.CharField(max_length=20, default='908')
    to_address = models.CharField(max_length=20)
    message = models.TextField()
    
    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    message_id = models.CharField(max_length=50, blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(blank=True, null=True)
    delivered_at = models.DateTimeField(blank=True, null=True)
    
    # User tracking
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    
    # Metadata
    cost = models.DecimalField(max_digits=10, decimal_places=4, default=0.0)
    character_count = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['to_address']),
        ]

    def save(self, *args, **kwargs):
        self.character_count = len(self.message)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"SMS to {self.to_address} - {self.status}"


class SMSTemplate(models.Model):
    name = models.CharField(max_length=100)
    content = models.TextField()
    variables = models.JSONField(default=list, help_text="List of variable names used in template")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class SMSCampaign(models.Model):
    name = models.CharField(max_length=200)
    template = models.ForeignKey(SMSTemplate, on_delete=models.CASCADE)
    recipients = models.TextField(help_text="Phone numbers separated by commas or newlines")
    scheduled_time = models.DateTimeField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    is_sent = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_recipient_count(self):
        recipients = [r.strip() for r in self.recipients.replace('\n', ',').split(',') if r.strip()]
        return len(recipients)