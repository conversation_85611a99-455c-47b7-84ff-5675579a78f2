{% extends 'base.html' %}

{% block title %}Send Bulk SMS - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Send Bulk SMS</h1>
            <p class="mt-2 text-gray-600">Send SMS messages to multiple recipients at once</p>
        </div>

        <!-- Form -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Bulk Message Details</h2>
            </div>
            <form method="post" class="p-6">
                {% csrf_token %}
                <div class="space-y-6">
                    <div>
                        <label for="{{ form.from_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>From Address
                        </label>
                        {{ form.from_address }}
                    </div>

                    <div>
                        <label for="{{ form.recipients.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-users mr-2"></i>Recipients
                        </label>
                        {{ form.recipients }}
                        <div class="mt-2">
                            <p class="text-xs text-gray-500">Enter phone numbers separated by commas or new lines</p>
                            <p class="text-xs text-gray-500 mt-1">
                                Recipients count: <span id="recipient-count" class="font-medium">0</span>
                            </p>
                        </div>
                    </div>

                    <div>
                        <label for="{{ form.message.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2"></i>Message
                        </label>
                        {{ form.message }}
                        <div class="flex justify-between mt-2">
                            <p class="text-xs text-gray-500">Maximum 160 characters</p>
                            <p class="text-xs text-gray-500">
                                <span id="char-count">0</span>/160 characters
                            </p>
                        </div>
                    </div>

                    <!-- Cost Estimate -->
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div class="flex items-center">
                            <i class="fas fa-calculator text-blue-600 mr-2"></i>
                            <h3 class="text-sm font-medium text-blue-900">Cost Estimate</h3>
                        </div>
                        <p class="text-sm text-blue-700 mt-1">
                            Estimated cost: $<span id="cost-estimate">0.00</span> 
                            (<span id="recipient-count-display">0</span> recipients × $0.05 per SMS)
                        </p>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{% url 'dashboard' %}" class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-3 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors duration-200">
                        <i class="fas fa-broadcast-tower mr-2"></i>Send Bulk SMS
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Character counter
    const messageInput = document.getElementById('{{ form.message.id_for_label }}');
    const charCount = document.getElementById('char-count');
    
    messageInput.addEventListener('input', function() {
        charCount.textContent = this.value.length;
        if (this.value.length > 160) {
            charCount.style.color = '#dc2626';
        } else {
            charCount.style.color = '#6b7280';
        }
    });
    
    // Recipient counter and cost calculator
    const recipientsInput = document.getElementById('{{ form.recipients.id_for_label }}');
    const recipientCount = document.getElementById('recipient-count');
    const recipientCountDisplay = document.getElementById('recipient-count-display');
    const costEstimate = document.getElementById('cost-estimate');
    
    function updateRecipientCount() {
        const recipients = recipientsInput.value.trim();
        if (!recipients) {
            recipientCount.textContent = '0';
            recipientCountDisplay.textContent = '0';
            costEstimate.textContent = '0.00';
            return;
        }
        
        const recipientList = recipients.split(/[,\n]/).filter(r => r.trim());
        const count = recipientList.length;
        
        recipientCount.textContent = count;
        recipientCountDisplay.textContent = count;
        costEstimate.textContent = (count * 0.05).toFixed(2);
    }
    
    recipientsInput.addEventListener('input', updateRecipientCount);
    
    // Initial counts
    charCount.textContent = messageInput.value.length;
    updateRecipientCount();
</script>
{% endblock %}