from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.db.models import Q, Count
from django.utils import timezone
from .models import SMSMessage, SMSTemplate, SMSCampaign
from .forms import SMSForm, SMSTemplateForm, SMSCampaignForm, BulkSMSForm
from .tasks import send_sms, send_bulk_sms


def dashboard(request):
    """Dashboard view with SMS statistics"""
    # Get statistics
    total_sms = SMSMessage.objects.count()
    sent_sms = SMSMessage.objects.filter(status='sent').count()
    delivered_sms = SMSMessage.objects.filter(status='delivered').count()
    failed_sms = SMSMessage.objects.filter(status='failed').count()
    
    # Recent messages
    recent_messages = SMSMessage.objects.all()[:10]
    
    # Status distribution for chart
    status_stats = SMSMessage.objects.values('status').annotate(count=Count('status'))
    
    context = {
        'total_sms': total_sms,
        'sent_sms': sent_sms,
        'delivered_sms': delivered_sms,
        'failed_sms': failed_sms,
        'recent_messages': recent_messages,
        'status_stats': status_stats,
    }
    return render(request, 'sms_app/dashboard.html', context)


@login_required
def send_single_sms(request):
    """Send a single SMS"""
    if request.method == 'POST':
        form = SMSForm(request.POST)
        if form.is_valid():
            sms_message = form.save(commit=False)
            sms_message.created_by = request.user
            sms_message.save()
            
            # Queue the SMS for sending
            send_sms.delay(sms_message.id)
            
            messages.success(request, 'SMS queued for sending!')
            return redirect('dashboard')
    else:
        form = SMSForm()
    
    return render(request, 'sms_app/send_sms.html', {'form': form})


@login_required
def send_bulk_sms_view(request):
    """Send bulk SMS"""
    if request.method == 'POST':
        form = BulkSMSForm(request.POST)
        if form.is_valid():
            recipients = [r.strip() for r in form.cleaned_data['recipients'].replace('\n', ',').split(',') if r.strip()]
            message = form.cleaned_data['message']
            from_address = form.cleaned_data['from_address']
            
            # Create SMS messages for each recipient
            sms_messages = []
            for recipient in recipients:
                sms_message = SMSMessage.objects.create(
                    from_address=from_address,
                    to_address=recipient,
                    message=message,
                    created_by=request.user,
                    status='pending'
                )
                sms_messages.append(sms_message)
                send_sms.delay(sms_message.id)
            
            messages.success(request, f'Queued {len(sms_messages)} SMS messages for sending!')
            return redirect('dashboard')
    else:
        form = BulkSMSForm()
    
    return render(request, 'sms_app/send_bulk_sms.html', {'form': form})


def sms_list(request):
    """List all SMS messages with pagination and filtering"""
    sms_messages = SMSMessage.objects.all()
    
    # Search functionality
    search = request.GET.get('search')
    if search:
        sms_messages = sms_messages.filter(
            Q(to_address__icontains=search) | 
            Q(message__icontains=search) |
            Q(message_id__icontains=search)
        )
    
    # Status filter
    status = request.GET.get('status')
    if status:
        sms_messages = sms_messages.filter(status=status)
    
    # Date filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        sms_messages = sms_messages.filter(created_at__date__gte=date_from)
    if date_to:
        sms_messages = sms_messages.filter(created_at__date__lte=date_to)
    
    # Pagination
    paginator = Paginator(sms_messages, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get status choices for filter dropdown
    status_choices = SMSMessage.STATUS_CHOICES
    
    context = {
        'page_obj': page_obj,
        'search': search,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': status_choices,
    }
    return render(request, 'sms_app/sms_list.html', context)


@login_required
def template_list(request):
    """List SMS templates"""
    templates = SMSTemplate.objects.filter(is_active=True)
    return render(request, 'sms_app/template_list.html', {'templates': templates})


@login_required
def create_template(request):
    """Create SMS template"""
    if request.method == 'POST':
        form = SMSTemplateForm(request.POST)
        if form.is_valid():
            template = form.save(commit=False)
            template.created_by = request.user
            template.save()
            messages.success(request, 'Template created successfully!')
            return redirect('template_list')
    else:
        form = SMSTemplateForm()
    
    return render(request, 'sms_app/create_template.html', {'form': form})


@login_required
def campaign_list(request):
    """List SMS campaigns"""
    campaigns = SMSCampaign.objects.all()
    return render(request, 'sms_app/campaign_list.html', {'campaigns': campaigns})


@login_required
def create_campaign(request):
    """Create SMS campaign"""
    if request.method == 'POST':
        form = SMSCampaignForm(request.POST)
        if form.is_valid():
            campaign = form.save(commit=False)
            campaign.created_by = request.user
            campaign.save()
            
            # If no scheduled time, send immediately
            if not campaign.scheduled_time:
                send_bulk_sms.delay(campaign.id)
                messages.success(request, f'Campaign "{campaign.name}" started!')
            else:
                messages.success(request, f'Campaign "{campaign.name}" scheduled!')
            
            return redirect('campaign_list')
    else:
        form = SMSCampaignForm()
        form.fields['template'].queryset = SMSTemplate.objects.filter(is_active=True)
    
    return render(request, 'sms_app/create_campaign.html', {'form': form})


def sms_detail(request, sms_id):
    """SMS message detail view"""
    sms_message = get_object_or_404(SMSMessage, id=sms_id)
    return render(request, 'sms_app/sms_detail.html', {'sms': sms_message})


def api_sms_status(request, sms_id):
    """API endpoint to get SMS status"""
    try:
        sms_message = SMSMessage.objects.get(id=sms_id)
        return JsonResponse({
            'status': sms_message.status,
            'message_id': sms_message.message_id,
            'sent_at': sms_message.sent_at.isoformat() if sms_message.sent_at else None,
            'delivered_at': sms_message.delivered_at.isoformat() if sms_message.delivered_at else None,
            'error_message': sms_message.error_message,
        })
    except SMSMessage.DoesNotExist:
        return JsonResponse({'error': 'SMS not found'}, status=404)