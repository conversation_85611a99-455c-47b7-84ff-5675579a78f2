{% extends 'base.html' %}

{% block title %}Dashboard - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">SMS Dashboard</h1>
        <p class="mt-2 text-gray-600">Monitor your SMS campaigns and track delivery statistics</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-sms text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4 w-0 flex-1">
                        <dt class="text-sm font-medium text-gray-500 truncate">Total SMS</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ total_sms|default:"0" }}</dd>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-paper-plane text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4 w-0 flex-1">
                        <dt class="text-sm font-medium text-gray-500 truncate">Sent</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ sent_sms|default:"0" }}</dd>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4 w-0 flex-1">
                        <dt class="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ delivered_sms|default:"0" }}</dd>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border border-gray-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-times-circle text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4 w-0 flex-1">
                        <dt class="text-sm font-medium text-gray-500 truncate">Failed</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ failed_sms|default:"0" }}</dd>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'send_sms' %}" class="group block p-6 bg-white rounded-lg border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-300">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                            <i class="fas fa-paper-plane text-blue-600 text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200">Send SMS</h3>
                        <p class="text-sm text-gray-500">Send a single SMS message</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'send_bulk_sms' %}" class="group block p-6 bg-white rounded-lg border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-200 hover:border-green-300">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                            <i class="fas fa-broadcast-tower text-green-600 text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900 group-hover:text-green-600 transition-colors duration-200">Bulk SMS</h3>
                        <p class="text-sm text-gray-500">Send SMS to multiple recipients</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'create_template' %}" class="group block p-6 bg-white rounded-lg border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-300">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                            <i class="fas fa-file-alt text-purple-600 text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900 group-hover:text-purple-600 transition-colors duration-200">Create Template</h3>
                        <p class="text-sm text-gray-500">Create reusable SMS templates</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Messages -->
    <div class="bg-white shadow-lg rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Recent Messages</h2>
                <a href="{% url 'sms_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</a>
            </div>
        </div>
        <div class="px-6 py-4">
            {% if recent_messages %}
                <div class="space-y-4">
                    {% for sms in recent_messages %}
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm font-medium text-gray-900">{{ sms.to_address }}</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if sms.status == 'sent' %}bg-green-100 text-green-800
                                        {% elif sms.status == 'delivered' %}bg-blue-100 text-blue-800
                                        {% elif sms.status == 'failed' %}bg-red-100 text-red-800
                                        {% elif sms.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ sms.get_status_display }}
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1 truncate">{{ sms.message|slice:":50" }}{% if sms.message|length > 50 %}...{% endif %}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500">{{ sms.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                    <p class="text-gray-500 mb-4">Start by sending your first SMS message</p>
                    <a href="{% url 'send_sms' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-paper-plane mr-2"></i>Send SMS
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}