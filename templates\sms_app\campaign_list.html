{% extends 'base.html' %}

{% block title %}SMS Campaigns - SMS Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">SMS Campaigns</h1>
                <p class="mt-2 text-gray-600">Manage your SMS marketing campaigns</p>
            </div>
            <a href="{% url 'create_campaign' %}" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>Create Campaign
            </a>
        </div>
    </div>

    <!-- Campaigns List -->
    {% if campaigns %}
        <div class="space-y-6">
            {% for campaign in campaigns %}
                <div class="bg-white shadow-lg rounded-lg border border-gray-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <h3 class="text-xl font-semibold text-gray-900">{{ campaign.name }}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if campaign.is_sent %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {% if campaign.is_sent %}Sent{% else %}Draft{% endif %}
                                </span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800 transition-colors duration-200">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Template</h4>
                                <p class="text-sm text-gray-600">{{ campaign.template.name }}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Recipients</h4>
                                <p class="text-sm text-gray-600">{{ campaign.get_recipient_count }} recipients</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Schedule</h4>
                                <p class="text-sm text-gray-600">
                                    {% if campaign.scheduled_time %}
                                        {{ campaign.scheduled_time|date:"M d, Y H:i" }}
                                    {% else %}
                                        Immediate
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <span>Created by {{ campaign.created_by.username }} on {{ campaign.created_at|date:"M d, Y" }}</span>
                                {% if not campaign.is_sent %}
                                    <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors duration-200">
                                        <i class="fas fa-play mr-1"></i>Send Now
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <i class="fas fa-bullhorn text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No campaigns yet</h3>
            <p class="text-gray-500 mb-6">Create your first SMS campaign to get started</p>
            <a href="{% url 'create_campaign' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                <i class="fas fa-plus mr-2"></i>Create Campaign
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}